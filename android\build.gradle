allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // Workaround Jetifier failing on bcprov-jdk18on 1.80 (class file version 65)
    configurations.all {
        resolutionStrategy.eachDependency { details ->
            if (details.requested.group == 'org.bouncycastle' &&
                    details.requested.name.startsWith('bcprov')) {
                details.useVersion '1.76'
                details.because '<PERSON>n <PERSON>le to 1.76 for Android Jetifier compatibility'
            }
        }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
