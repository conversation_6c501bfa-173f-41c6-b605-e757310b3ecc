# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Java heap dumps and profiling files
*.hprof
java_pid*.hprof 
android/app/.cxx/RelWithDebInfo/344i2t51/x86_64/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake
android/app/.cxx/RelWithDebInfo/344i2t51/x86_64/CMakeFiles/3.22.1-g37088a8-dirty/CMakeDetermineCompilerABI_C.bin
android/app/.cxx/Debug/4v4b2o5u/x86_64/.cmake/api/v1/query/client-agp/cache-v2
