part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const ON_BOARDING = _Paths.ON_BOARDING;
  static const SIGN_UP = _Paths.SIGN_UP;
  static const OTP_SCREEN = _Paths.OTP_SCREEN;
  static const PHONE_VERIFICATION = _Paths.PHONE_VERIFICATION;
  static const HOME = _Paths.HOME;
  static const SUBSCRIPTION = _Paths.SUBSCRIPTION;
  static const NOTIFICATIONS = _Paths.NOTIFICATIONS;
  static const TRACK_SELECTION = _Paths.TRACK_SELECTION;
  static const LEARN_SCREEN = _Paths.LEARN_SCREEN;
  static const COMMUNITY_FORUM = _Paths.COMMUNITY_FORUM;
  static const PAYMENT_SCREEN = _Paths.PAYMENT_SCREEN;
  static const PROFILE = _Paths.PROFILE;
  static const DASHBOARD = _Paths.DASHBOARD;
  static const DEACTIVATE_SCREEN = _Paths.DEACTIVATE_SCREEN;
  static const firstTimeSplash = _Paths.firstTimeSplash;
  static const GreetingSplashView = _Paths.GreetingSplashView;
  static const welcomeSplash = _Paths.welcomeSplash;
  static const SIGNOUT_SCREEN = _Paths.SIGNOUT_SCREEN;
  static const KNOWLEDGE_CENTER_SCREEN = _Paths.KNOWLEDGE_CENTER_SCREEN;
  static const WORLD_AND_CULTURE_SCREEN = _Paths.worldAndCultureScreen;
  static const PERSONAL_GROWTH_SCREEN = _Paths.personalGrowthScreen;
  static const BUSINESS_INNOVATION_SCREEN = _Paths.businessInnovationScreen;
  static const NEWS_SCREEN = _Paths.newsScreen;
  static const NEWS_DETAILS_SCREEN = _Paths.newsDetailsScreen;
  static const WORLD_AND_CULTURE_COMMUNITY_SCREEN =
      _Paths.worldAndCultureCommunityScreen;
  static const PERSONAL_GROWTH_COMMUNITY_SCREEN =
      _Paths.personalGrowthCommunityScreen;
  static const BUSINESS_INNOVATION_COMMUNITY_SCREEN =
      _Paths.businessInnovationCommunityScreen;
  static const CONSENT_SCREEN = _Paths.consentScreen;
  static const MESSAGE_SCREEN = _Paths.messageScreen;
}

abstract class _Paths {
  _Paths._();
  static const ON_BOARDING = '/on-boarding';
  static const SIGN_UP = '/sign-up';
  static const OTP_SCREEN = '/otp-screen';
  static const PHONE_VERIFICATION = '/phone-verification';
  static const HOME = '/home';
  static const SUBSCRIPTION = '/subscription';
  static const NOTIFICATIONS = '/notifications';
  static const TRACK_SELECTION = '/track-selection';
  static const LEARN_SCREEN = '/learn-screen';
  static const COMMUNITY_FORUM = '/community-forum';
  static const PAYMENT_SCREEN = '/payment-screen';
  static const PROFILE = '/profile';
  static const DASHBOARD = '/dashboard';
  static const DEACTIVATE_SCREEN = '/deactivate-screen';
  static const firstTimeSplash = '/first-time-splash';
  static const GreetingSplashView = '/greeting-splash-view';
  static const welcomeSplash = '/welcome-splash';
  static const SIGNOUT_SCREEN = '/signout-screen';
  static const KNOWLEDGE_CENTER_SCREEN = '/knowledge-center-screen';
  static const worldAndCultureScreen = '/world-and-culture-screen';
  static const personalGrowthScreen = '/personal-growth-screen';
  static const businessInnovationScreen = '/business-innovation-screen';
  static const newsScreen = '/news-screen';
  static const newsDetailsScreen = '/news-details-screen';
  static const worldAndCultureCommunityScreen =
      '/world-and-culture-community-screen';
  static const personalGrowthCommunityScreen =
      '/personal-growth-community-screen';
  static const businessInnovationCommunityScreen =
      '/business-innovation-community-screen';
  static const consentScreen = '/consent-screen';
  static const messageScreen = '/message-screen';
}
