import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vropay_final/app/core/models/user_model.dart';
import 'package:vropay_final/app/core/services/auth_service.dart';
// import 'package:vropay_final/app/core/services/user_service.dart';
import 'package:vropay_final/app/routes/app_pages.dart';

class ProfileController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  // Keep reference only if needed elsewhere
  // final UserService _userService = Get.find<UserService>();

  // Text controllers
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController mobileController = TextEditingController();
  TextEditingController professionController = TextEditingController();

  // Observable variables
  var isLoading = false.obs;
  var user = Rxn<UserModel>();
  // Edit modes expected by view
  final RxBool isGeneralEditMode = false.obs;
  final RxBool isPreferencesEditMode = false.obs;
  var selectedGender = 'Male'.obs;
  // Backend list of topics and UI label for topics
  var selectedTopicsList = <String>[].obs;
  var selectedTopics = ''.obs;
  var difficultyLevel = 'Beginner'.obs;
  var communityAccess = 'Public'.obs;
  var notificationsEnabled = true.obs;

  // Options expected by view
  final List<String> genderOptions = ['Male', 'Female', 'Other'];
  final List<String> categoryOptions = [
    'Business owner',
    'Student',
    'Working Professional',
  ];
  final List<String> topicsOptions = ['Selected', 'All', 'None'];
  final List<String> difficultyOptions = [
    'Beginner',
    'Intermediate',
    'Advance'
  ];
  final List<String> communityOptions = ['In', 'Out'];
  final List<String> notificationOptions = ['Allowed', 'Blocked'];

  // Selected values expected by view
  var selectedCategory = 'Business owner'.obs;
  var selectedDifficulty = 'Beginner'.obs;
  var selectedCommunity = 'In'.obs; // maps to communityAccess
  var selectedNotifications = 'Allowed'.obs; // maps to notificationsEnabled

  @override
  void onInit() {
    super.onInit();
    loadUserData();
  }

  // Load user data from backend
  Future<void> loadUserData() async {
    try {
      isLoading.value = true;
      final response = await _authService.getUserProfile();
      final UserModel? userData = response.data;
      if (userData != null) {
        user.value = userData;
        firstNameController.text = userData.firstName ?? '';
        lastNameController.text = userData.lastName ?? '';
        emailController.text = userData.email;
        mobileController.text = userData.mobile ?? '';
        professionController.text = userData.profession ?? '';
        selectedGender.value = userData.gender ?? 'Male';
        selectedTopicsList.value = userData.selectedTopics ?? [];
        selectedTopics.value = selectedTopicsList.join(', ');
        difficultyLevel.value = userData.difficultyLevel ?? 'Beginner';
        communityAccess.value = userData.communityAccess ?? 'Public';
        notificationsEnabled.value = userData.notificationsEnabled ?? true;
        // Reflect backend prefs into UI selections
        selectedDifficulty.value = difficultyLevel.value;
        selectedCommunity.value =
            communityAccess.value == 'Public' ? 'In' : 'Out';
        selectedNotifications.value =
            notificationsEnabled.value ? 'Allowed' : 'Blocked';
      }
    } catch (e) {
      print('Error loading user data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Update user profile
  Future<void> updateProfile() async {
    try {
      isLoading.value = true;
      await _authService.updateUserProfile(
        firstName: firstNameController.text,
        lastName: lastNameController.text,
        mobile: mobileController.text,
        profession: professionController.text,
        gender: selectedGender.value,
        selectedTopics: selectedTopicsList.toList(),
        difficultyLevel: selectedDifficulty.value,
        communityAccess: selectedCommunity.value == 'In' ? 'Public' : 'Private',
        notificationsEnabled: selectedNotifications.value == 'Allowed',
      );
      Get.snackbar('Success', 'Profile updated successfully');
    } catch (e) {
      Get.snackbar('Error', 'Failed to update profile: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _authService.logout();
      Get.offAllNamed(Routes.ON_BOARDING);
    } catch (e) {
      Get.snackbar('Error', 'Failed to sign out: $e');
    }
  }

  // Update gender
  void updateGender(String gender) {
    selectedGender.value = gender;
  }

  // Update topics
  void updateTopics(List<String> topics) {
    selectedTopicsList.value = topics;
    selectedTopics.value = topics.join(', ');
  }

  // Update difficulty level
  void updateDifficultyLevel(String level) {
    difficultyLevel.value = level;
    selectedDifficulty.value = level;
  }

  // Update community access
  void updateCommunityAccess(String access) {
    communityAccess.value = access == 'In' ? 'Public' : 'Private';
    selectedCommunity.value = access;
  }

  // Update notifications
  void updateNotifications(bool enabled) {
    notificationsEnabled.value = enabled;
    selectedNotifications.value = enabled ? 'Allowed' : 'Blocked';
  }

  // Expected by view edit buttons
  void saveGeneralProfile() {
    updateProfile();
  }

  void savePreferences() {
    updateProfile();
  }
}
