{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Development\\vropay-app\\android\\app\\.cxx\\Debug\\4v4b2o5u\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Development\\vropay-app\\android\\app\\.cxx\\Debug\\4v4b2o5u\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}